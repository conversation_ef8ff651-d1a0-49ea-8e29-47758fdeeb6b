import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { Connection, Model, connect } from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import {
  Rule,
  RuleSchema,
  Event,
  EventSchema,
  Conditions,
  ConditionsSchema,
  ConditionsKindNamesEnum,
  ServiceToPackageMap,
  ServiceToPackageMapSchema,
  AllConditions,
  AllConditionsSchema,
  AnyConditions,
  AnyConditionsSchema,
  RulesPackages,
  RulesPackagesSchema
} from 'ads-layouts-tools';
import { RuleService } from './rules.service';
import { RulesPackagesService } from '../rulesPackages/rulesPackages.service';
import { ServiceToPackageMapService } from '../serviceToPackageMap/serviceToPackageMap.service';

describe('RuleService - copyRulesForSpecificPackage', () => {
  let mongod: MongoMemoryServer;
  let mongoConnection: Connection;
  let service: RuleService;
  let mongoRuleModel: Model<Rule>;
  let mongoEventModel: Model<Event>;
  let mongoConditionsModel: Model<Conditions>;
  let mongoServiceToPackageMapModel: Model<ServiceToPackageMap>;
  let rulesPackageModel: Model<RulesPackages>;

  beforeAll(async () => {
    mongod = await MongoMemoryServer.create();
    const uri = mongod.getUri();
    mongoConnection = (await connect(uri)).connection;

    mongoRuleModel = mongoConnection.model(Rule.name, RuleSchema);
    mongoEventModel = mongoConnection.model(Event.name, EventSchema);
    mongoConditionsModel = mongoConnection.model(Conditions.name, ConditionsSchema);
    mongoConditionsModel.discriminator(AnyConditions.name, AnyConditionsSchema);
    mongoConditionsModel.discriminator(AllConditions.name, AllConditionsSchema);
    rulesPackageModel = mongoConnection.model(RulesPackages.name, RulesPackagesSchema);
    mongoServiceToPackageMapModel = mongoConnection.model(
      ServiceToPackageMap.name,
      ServiceToPackageMapSchema
    );

    const app: TestingModule = await Test.createTestingModule({
      providers: [
        RuleService,
        RulesPackagesService,
        ServiceToPackageMapService,
        {
          provide: getModelToken(Rule.name),
          useValue: mongoRuleModel
        },
        {
          provide: getModelToken(Event.name),
          useValue: mongoEventModel
        },
        {
          provide: getModelToken(Conditions.name),
          useValue: mongoConditionsModel
        },
        {
          provide: getModelToken(ServiceToPackageMap.name),
          useValue: mongoServiceToPackageMapModel
        },
        {
          provide: getModelToken(RulesPackages.name),
          useValue: rulesPackageModel
        }
      ]
    }).compile();

    service = app.get(RuleService);
  });

  afterAll(async () => {
    await mongoConnection.close();
    await mongod.stop();
  });

  beforeEach(async () => {
    await mongoRuleModel.deleteMany({});
    await mongoEventModel.deleteMany({});
    await mongoConditionsModel.deleteMany({});
  });

  describe('copyRulesForSpecificPackage', () => {
    it('should copy conditions with correct kind property', async () => {
      // Create test data
      const testEvent = await mongoEventModel.create({
        name: 'testEvent',
        eventType: 'test'
      });

      const testAllCondition = await mongoConditionsModel.create({
        name: 'testAllCondition',
        all: [{ fact: 'test', operator: 'equal', value: 'test' }],
        kind: ConditionsKindNamesEnum.All
      });

      const testAnyCondition = await mongoConditionsModel.create({
        name: 'testAnyCondition',
        any: [{ fact: 'test', operator: 'equal', value: 'test' }],
        kind: ConditionsKindNamesEnum.Any
      });

      const testRule = await mongoRuleModel.create({
        name: 'testRule',
        eventName: 'testEvent',
        conditionsName: 'testAllCondition',
        event: testEvent._id,
        conditions: testAllCondition._id,
        serviceId: 'test'
      });

      // Test copying to a new package
      const result = await service.copyRulesForSpecificPackage('newPackage', ['testRule']);

      expect(result.type).toBe('SUCCESS');

      // Verify that conditions were copied with correct kind property
      const copiedConditions = await mongoConditionsModel.find({ rulesPackage: 'newPackage' });
      expect(copiedConditions).toHaveLength(1);
      expect(copiedConditions[0].kind).toBe(ConditionsKindNamesEnum.All);
      expect(copiedConditions[0].name).toBe('testAllCondition');
    });

    it('should handle both All and Any conditions correctly', async () => {
      // Create test data with both All and Any conditions
      const testEvent1 = await mongoEventModel.create({
        name: 'testEvent1',
        eventType: 'test'
      });

      const testEvent2 = await mongoEventModel.create({
        name: 'testEvent2',
        eventType: 'test'
      });

      const testAllCondition = await mongoConditionsModel.create({
        name: 'testAllCondition',
        all: [{ fact: 'test', operator: 'equal', value: 'test' }],
        kind: ConditionsKindNamesEnum.All
      });

      const testAnyCondition = await mongoConditionsModel.create({
        name: 'testAnyCondition',
        any: [{ fact: 'test', operator: 'equal', value: 'test' }],
        kind: ConditionsKindNamesEnum.Any
      });

      await mongoRuleModel.create({
        name: 'testRule1',
        eventName: 'testEvent1',
        conditionsName: 'testAllCondition',
        event: testEvent1._id,
        conditions: testAllCondition._id,
        serviceId: 'test'
      });

      await mongoRuleModel.create({
        name: 'testRule2',
        eventName: 'testEvent2',
        conditionsName: 'testAnyCondition',
        event: testEvent2._id,
        conditions: testAnyCondition._id,
        serviceId: 'test'
      });

      // Test copying both rules
      const result = await service.copyRulesForSpecificPackage('newPackage', [
        'testRule1',
        'testRule2'
      ]);

      expect(result.type).toBe('SUCCESS');

      // Verify that both conditions were copied with correct kind properties
      const copiedConditions = await mongoConditionsModel.find({ rulesPackage: 'newPackage' });
      expect(copiedConditions).toHaveLength(2);

      const allCondition = copiedConditions.find(c => c.name === 'testAllCondition');
      const anyCondition = copiedConditions.find(c => c.name === 'testAnyCondition');

      expect(allCondition?.kind).toBe(ConditionsKindNamesEnum.All);
      expect(anyCondition?.kind).toBe(ConditionsKindNamesEnum.Any);
    });
  });
});
