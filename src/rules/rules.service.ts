import {
  BadRequestException,
  forwardRef,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  NotFoundException
} from '@nestjs/common';
import { InjectConnection, InjectModel } from '@nestjs/mongoose';
import {
  AllConditions,
  AllConditionsDocument,
  AnyConditions,
  AnyConditionsDocument,
  Conditions,
  ConditionsDocument,
  ConditionsKindNamesEnum,
  Event,
  EventDocument,
  LogLevel,
  ResponseInterface,
  ResponseType,
  Rule,
  RuleDocument,
  SearchQueryName
} from 'ads-layouts-tools';
import {
  createResponse,
  getArrayDifference,
  getFetchRuleOptions,
  getLikeQueryOptions,
  useFreezeChecker
} from 'Helpers';
import { ClientSession, Connection, HydratedDocument, Model, QueryOptions } from 'mongoose';
import { CreateException, log } from 'Utils';
import { RulesPackagesService } from '../rulesPackages/rulesPackages.service';
import { cloneRule } from './dto/cloneRule.dto';

type MyProcessedOutput<T, Condition extends boolean | undefined> = Condition extends undefined
  ? T
  : Condition extends true
    ? string[]
    : T;

@Injectable()
export class RuleService {
  constructor(
    @Inject(forwardRef(() => RulesPackagesService))
    private readonly rulesPackagesService: RulesPackagesService,
    @InjectModel(Rule.name) private ruleModel: Model<RuleDocument>,
    @InjectModel(Event.name) private eventModel: Model<EventDocument>,
    @InjectModel(Conditions.name) private conditionsModel: Model<ConditionsDocument>,
    @InjectModel(AllConditions.name) private allConditionsModel: Model<AllConditionsDocument>,
    @InjectModel(AnyConditions.name) private anyConditionsModel: Model<AnyConditionsDocument>,
    @InjectConnection() private readonly connection: Connection
  ) {}

  async createRule(rule: Rule): Promise<Rule> {
    try {
      const relatedEvent = await this.eventModel.findOne({
        name: rule.eventName,
        rulesPackage: { $exists: false }
      });

      if (!relatedEvent) {
        throw new BadRequestException('Event not found');
      }

      const relatedConditions = await this.conditionsModel.findOne({
        name: rule.conditionsName,
        rulesPackage: { $exists: false }
      });

      if (!relatedConditions) {
        throw new BadRequestException('Condition not found');
      }

      const newRule = await new this.ruleModel({
        ...rule,
        event: relatedEvent._id,
        conditions: relatedConditions._id
      }).populate(['event', 'conditions']);

      const createNew = await newRule.save();
      return createNew;
    } catch (err) {
      if (err instanceof HttpException) {
        throw err;
      }

      throw new BadRequestException(err?.message);
    }
  }

  @useFreezeChecker({ key: 1, isSearchQuery: false })
  async updateRule(
    name: string,
    rulesPackage: string | undefined,
    updateRuleDto: any
  ): Promise<RuleDocument> {
    const options = getFetchRuleOptions(name, rulesPackage);

    if (updateRuleDto.eventName) {
      updateRuleDto.event = await this.findEntity(
        name,
        updateRuleDto.eventName,
        rulesPackage,
        this.eventModel,
        'Event'
      );
    }

    if (updateRuleDto.conditionsName) {
      updateRuleDto.conditions = await this.findEntity(
        name,
        updateRuleDto.conditionsName,
        rulesPackage,
        this.conditionsModel,
        'Condition'
      );
    }

    const existingRule = await this.ruleModel
      .findOneAndUpdate(options, updateRuleDto, { new: true })
      .populate(['event', 'conditions'])
      .exec();

    if (!existingRule) {
      throw new NotFoundException(
        `Rule ${name} ${rulesPackage ? 'with rulesPackage = ' + rulesPackage + ' ' : ''}not updated`
      );
    }

    return existingRule;
  }

  async findEntity<
    T extends { name: string; rulesPackage?: string },
    D extends HydratedDocument<T>
  >(
    ruleName: string,
    entityName: string,
    rulesPackage: string | undefined,
    model: Model<D>,
    entityType: 'Event' | 'Condition'
  ): Promise<D['_id']> {
    const entity = await model.findOne({
      name: entityName,
      rulesPackage: rulesPackage || { $exists: false }
    });

    if (!entity) {
      throw new BadRequestException(
        `${entityType} '${entityName}' in package '${rulesPackage || 'original'}' not found so rule '${ruleName}' can't be updated`
      );
    }

    return entity._id;
  }

  async getAllRules<Condition extends boolean | undefined>(
    searchQuery: SearchQueryName,
    onlyNames?: Condition
  ): Promise<MyProcessedOutput<RuleDocument[], Condition>> {
    const options = getLikeQueryOptions(['name', 'rulesPackage', 'serviceId'], searchQuery);

    const ruleData: RuleDocument[] = await this.ruleModel
      .find(options)
      .populate(['event', 'conditions'])
      .exec();

    if (!ruleData || ruleData.length == 0) {
      throw CreateException({
        message: 'Rules data not found',
        statusCode: HttpStatus.NOT_FOUND
      });
    }

    return (
      onlyNames
        ? ruleData.map(rule => `${rule.name} - ${rule.rulesPackage || 'original'}`)
        : ruleData
    ) as MyProcessedOutput<RuleDocument[], Condition>;
  }

  async getRulesWithPlaceholdersGroups(): Promise<RuleDocument[]> {
    const ruleData = await this.ruleModel
      .find({})
      .populate({
        path: 'event',
        select: '-createdAt -updatedAt -type -params.fact -params.placeholder -_id -__v'
      })
      .select('-conditionsName -conditions -eventName -createdAt -updatedAt -_id -__v')
      .exec();

    if (!ruleData || ruleData.length == 0) {
      throw new NotFoundException('Rules data not found');
    }
    return ruleData;
  }

  async getPageRules(pageType: string): Promise<RuleDocument[]> {
    const RuleData = await this.ruleModel
      .find({ pageType })
      .select('-createdAt -updatedAt -_id -__v')
      .populate({
        path: 'event adConfig conditions',
        select: '-createdAt -updatedAt -_id -__v -all._id -any._id -name -desc',
        strictPopulate: false
      })
      .exec();

    if (!RuleData || RuleData.length == 0) {
      throw new NotFoundException('Rules data not found');
    }
    return RuleData;
  }

  async getRule(
    name: string,
    searchQuery: SearchQueryName,
    rulesPackage?: string
  ): Promise<RuleDocument> {
    const options = getLikeQueryOptions(['rulesPackage', 'serviceId'], searchQuery);

    const filter = {
      name,
      rulesPackage,
      ...options
    };

    const existingRule = await this.ruleModel
      .findOne(filter)
      .populate(['event', 'conditions'])
      .exec();
    if (!existingRule) {
      const infoAboutOptions = Object.keys(filter).length
        ? `with options ${JSON.stringify(filter)} `
        : '';
      throw new NotFoundException(`Rule ${name} ${infoAboutOptions}not found`);
    }
    return existingRule;
  }

  async getMissingRulesFromList(rulesList: string[]): Promise<string[]> {
    const rulesData = await this.ruleModel.find({
      name: { $in: rulesList }
    });

    const missingRules = getArrayDifference(
      rulesList,
      rulesData.map(rule => rule.name)
    );

    return missingRules;
  }

  @useFreezeChecker({ key: 1, isSearchQuery: false })
  async deleteRule(
    name: string,
    rulesPackage: string | undefined
  ): Promise<ResponseInterface> {
    const deletedRulesResult: ResponseInterface = await this.deleteRulesWithSpecificPackage(
      rulesPackage,
      [name]
    );

    if (deletedRulesResult.type === ResponseType.ERROR) {
      log('ERROR_RULE_DELETE', { error: deletedRulesResult.message }, LogLevel.error);

      throw CreateException({
        message: deletedRulesResult.message,
        statusCode: HttpStatus.BAD_REQUEST
      });
    }

    return deletedRulesResult;
  }

  @useFreezeChecker({ key: 0, isSearchQuery: true })
  async deleteAllRules(searchQuery: SearchQueryName) {
    if (searchQuery.rulesPackage && /^%.*%$/.test(searchQuery.rulesPackage)) {
      log(
        'RULE_DELETE_NOT_ALLOWED_QUERY_PARAM',
        { RulesPackage: searchQuery.rulesPackage },
        LogLevel.error
      );

      throw CreateException({
        message: 'Using queryLike %% in query params is not allowed on rulesPackage field',
        statusCode: HttpStatus.BAD_REQUEST
      });
    }

    const options = getLikeQueryOptions(['name', 'rulesPackage'], searchQuery, true);
    const allRules = await this.ruleModel.find(options);

    if (allRules.length === 0) {
      const messageParts: string[] = [];

      if (searchQuery.name) {
        messageParts.push(`Names like ${searchQuery.name}`);
      }

      if (searchQuery.rulesPackage) {
        const firstLetter = `${searchQuery.name ? 'i' : 'I'}`;
        messageParts.push(`${firstLetter}n package ${searchQuery.rulesPackage}`);
      }

      const message =
        messageParts.length > 0
          ? `Rules to match pattern: ${messageParts.join(', ')}, were not found. Nothing to delete.`
          : `No rule in database. Nothing to delete.`;

      return createResponse(HttpStatus.OK, message, { count: 0 });
    }

    const deletedRulesResult: ResponseInterface = await this.deleteRulesWithSpecificPackage(
      searchQuery.rulesPackage,
      allRules.map(rule => rule.name)
    );

    if (deletedRulesResult.type === ResponseType.ERROR) {
      log('ERROR_RULE_DELETE', { error: deletedRulesResult.message }, LogLevel.error);

      throw CreateException({
        message: deletedRulesResult.message,
        statusCode: HttpStatus.BAD_REQUEST
      });
    }

    return deletedRulesResult;
  }

  @useFreezeChecker({ key: 0, isSearchQuery: false })
  async deleteRulesByPackageName(rulesPackageName: string): Promise<ResponseInterface> {
    const transactionSession: ClientSession = await this.connection.startSession();

    try {
      const rulesCount = await this.ruleModel.countDocuments({
        rulesPackage: rulesPackageName
      });

      if (rulesCount === 0) {
        return createResponse(
          HttpStatus.NOT_MODIFIED,
          `No rules found in package: ${rulesPackageName}. Nothing to delete.`
        );
      }

      transactionSession.startTransaction();

      const deletedRules = await this.ruleModel.deleteMany(
        { rulesPackage: rulesPackageName },
        { session: transactionSession }
      );

      const deletedEvents = await this.eventModel.deleteMany(
        { rulesPackage: rulesPackageName },
        { session: transactionSession }
      );

      const deletedConditions = await this.conditionsModel.deleteMany(
        { rulesPackage: rulesPackageName },
        { session: transactionSession }
      );

      await transactionSession.commitTransaction();

      return createResponse(
        HttpStatus.OK,
        [
          'Transaction successfully deleted:',
          `${deletedRules.deletedCount || 0} rules,`,
          `${deletedEvents.deletedCount || 0} events,`,
          `${deletedConditions.deletedCount || 0} conditions`,
          `for packageName ${rulesPackageName}!`
        ].join(' ')
      );
    } catch (error) {
      await transactionSession.abortTransaction();

      throw CreateException({
        message: `Transaction error while deleting rules, events and conditions. Message: ${error.message}`,
        statusCode: HttpStatus.BAD_REQUEST
      });
    } finally {
      await transactionSession.endSession();
    }
  }

  async deleteRulesWithSpecificPackage(
    packageName: string | undefined,
    rulesToRemove: string[]
  ): Promise<ResponseInterface> {
    /*
     *  General principle of operation is as follows:
     *  - get all rules to remove
     *  - get events/conditions ids assigned to this rules
     *  - get rules with this events/conditions outside rules to remove list
     *  - events/conditions from this rules won't be removed
     *  - remove events/conditions which are present in eventsFromRulesToRemove collection and not in eventsNotToRemove only
     *  - don't remove events/conditions which are present only in original space
     */

    const rulesToRemoveObject = await this.ruleModel.find({
      rulesPackage: packageName,
      name: { $in: rulesToRemove }
    });

    if (rulesToRemoveObject.length === 0) {
      const message = `None of rules ${rulesToRemove.join(', ')} were found in package ${packageName} so can't be deleted.`;

      log('RULES_DELETE_INFO', { message }, LogLevel.info);

      throw CreateException({ message, statusCode: HttpStatus.OK });
    }

    let eventsToRemove: string[] = [];
    let conditionsToRemove: string[] = [];

    if (packageName) {
      const rulesToRemoveIds = rulesToRemoveObject.map(rule => rule._id.toString());

      const eventsFromRulesToRemove = rulesToRemoveObject
        .filter(rule => rule?.eventName)
        .map(rule => rule.event.toString());

      const conditionsFromRulesToRemove = rulesToRemoveObject
        .filter(rule => rule?.conditionsName)
        .map(rule => rule.conditions.toString());

      const rulesWithEventsToRemoveOutsideRemoveList = await this.ruleModel.find({
        $and: [
          { _id: { $nin: rulesToRemoveIds } },
          { event: { $in: eventsFromRulesToRemove } }
        ]
      });

      const rulesWithConditionsToRemoveOutsideRemoveList = await this.ruleModel.find({
        $and: [
          { _id: { $nin: rulesToRemoveIds } },
          { conditions: { $in: conditionsFromRulesToRemove } }
        ]
      });

      const eventsNotToRemove = rulesWithEventsToRemoveOutsideRemoveList
        .filter(rule => rule?.eventName)
        .map(rule => rule.event.toString());

      const conditionsNotToRemove = rulesWithConditionsToRemoveOutsideRemoveList
        .filter(rule => rule?.conditionsName)
        .map(rule => rule.conditions.toString());

      eventsToRemove = getArrayDifference(eventsFromRulesToRemove, eventsNotToRemove);
      conditionsToRemove = getArrayDifference(
        conditionsFromRulesToRemove,
        conditionsNotToRemove
      );
    }

    const transactionSession: ClientSession = await this.connection.startSession();

    try {
      transactionSession.startTransaction();

      const deletedRules = await this.ruleModel.deleteMany(
        { rulesPackage: packageName, name: { $in: rulesToRemove } },
        { session: transactionSession }
      );

      if (eventsToRemove.length > 0) {
        await this.eventModel.deleteMany(
          { rulesPackage: packageName, _id: { $in: eventsToRemove } },
          { session: transactionSession }
        );
      }

      if (conditionsToRemove.length > 0) {
        await this.conditionsModel.deleteMany(
          { rulesPackage: packageName, _id: { $in: conditionsToRemove } },
          { session: transactionSession }
        );
      }

      await transactionSession.commitTransaction();

      return createResponse(
        HttpStatus.OK,
        `Rules ${packageName ? '(and events and conditions) ' : ''}${rulesToRemove.join(', ')} from packageName ${packageName || 'original'} deleted successfully!`,
        deletedRules
      );
    } catch (error) {
      await transactionSession.abortTransaction();

      return createResponse(
        HttpStatus.BAD_REQUEST,
        `Error while deleting rules, events, conditions. Error: ${error}`
      );
    } finally {
      await transactionSession.endSession();
    }
  }

  async copyRulesForSpecificPackage(
    packageName: string,
    rulesToCopy: string[],
    packageNameCopyFrom?: string
  ): Promise<ResponseInterface> {
    const transactionSession: ClientSession = await this.connection.startSession();

    try {
      const foundRules = await this.ruleModel
        .find({
          name: { $in: rulesToCopy },
          rulesPackage: packageNameCopyFrom || { $exists: false }
        })
        .select('-createdAt -updatedAt -_id -__v');

      const eventsIds = Array.from(
        new Set(foundRules.map((rule: Rule): string => rule.event?.toString()))
      );

      const conditionsIds = Array.from(
        new Set(foundRules.map((rule: Rule) => rule.conditions?.toString()))
      );

      const foundEvents = await this.eventModel
        .find({
          _id: { $in: eventsIds }
        })
        .select('-createdAt -updatedAt -__v');

      const foundConditions = await this.conditionsModel
        .find({
          _id: { $in: conditionsIds }
        })
        .select('-createdAt -updatedAt -__v');

      transactionSession.startTransaction();

      // Always should exist all original events and conditions when we want to copy rules
      if (
        eventsIds.length !== foundEvents.length ||
        conditionsIds.length !== foundConditions.length
      ) {
        const missingEventsIds = getArrayDifference(
          eventsIds,
          foundEvents.map((event: EventDocument) => event._id?.toString())
        );

        const missingConditionsIds = getArrayDifference(
          conditionsIds,
          foundConditions.map((condition: ConditionsDocument) => condition._id?.toString())
        );

        throw CreateException({
          message: `This events ids: [${missingEventsIds}] and this conditions ids: [${missingConditionsIds}] is not exist in ${packageNameCopyFrom || 'original'} rules package space`,
          statusCode: HttpStatus.NOT_FOUND
        });
      }

      // I don't need copy events/conditions which already exist in package, because they will fire unique name&rulesPackage error when mongodb try to insert them.
      const allEventsInPackage = await this.eventModel
        .find({ rulesPackage: packageName })
        .select('name');

      const allConditionsInPackage = await this.conditionsModel
        .find({ rulesPackage: packageName })
        .select('name');

      const copiedEvents = foundEvents
        .filter(
          // if event not exist in package already
          event => !allEventsInPackage.map(event => event.name).includes(event.name)
        )
        .map((event: EventDocument) => {
          const { _id, ...rest } = event.toObject();

          return { ...rest, rulesPackage: packageName };
        });

      const { allConditions, anyConditions } = foundConditions
        .filter(
          // if condition not exist in package already
          condition =>
            !allConditionsInPackage.map(condition => condition.name).includes(condition.name)
        )
        .map((condition: ConditionsDocument) => {
          const { _id, ...rest } = condition.toObject();

          return { ...rest, rulesPackage: packageName };
        })
        .reduce<{ allConditions: AllConditions[]; anyConditions: AnyConditions[] }>(
          (acc, curr) => {
            if (curr.kind === ConditionsKindNamesEnum.All) {
              acc.allConditions.push(curr);
            } else {
              acc.anyConditions.push(curr);
            }
            return acc;
          },
          { allConditions: [], anyConditions: [] }
        );

      await this.eventModel.insertMany(copiedEvents, {
        session: transactionSession
      });

      if (allConditions.length > 0) {
        await this.allConditionsModel.insertMany(allConditions, {
          session: transactionSession
        });
      }
      if (anyConditions.length > 0) {
        await this.anyConditionsModel.insertMany(anyConditions, {
          session: transactionSession
        });
      }

      // fetch inserted just before events and conditions because copyResult after insert doesn't provide _id
      const insertedEvents = await this.eventModel.find({ rulesPackage: packageName }, null, {
        session: transactionSession
      });

      const insertedConditions = await this.conditionsModel.find(
        { rulesPackage: packageName },
        null,
        {
          session: transactionSession
        }
      );

      const copiedRules = foundRules.map((rule: Rule) => {
        rule.rulesPackage = packageName;
        rule.event = insertedEvents.find(event => event.name === rule.eventName)!;
        rule.conditions = insertedConditions.find(
          condition => condition.name === rule.conditionsName
        )!;

        return rule;
      });

      const copyResultRules: RuleDocument[] = await this.ruleModel.insertMany(copiedRules, {
        session: transactionSession
      });

      await transactionSession.commitTransaction();

      return createResponse(
        HttpStatus.OK,
        'Rules copied. RulesPackage created/updated successfully!',
        {
          copyResultRules,
          insertedEvents,
          insertedConditions
        }
      );
    } catch (error) {
      await transactionSession.abortTransaction();

      return createResponse(
        HttpStatus.BAD_REQUEST,
        `Error while copying rules. Message: ${error.message}. No rules copied. Revert creating/updating rulesPackage`
      );
    } finally {
      await transactionSession.endSession();
    }
  }

  async getRulesForPackage(packageName: string): Promise<RuleDocument[]> {
    const rules = await this.ruleModel
      .find({
        rulesPackage: packageName
      })
      .select('-createdAt -updatedAt -_id -__v')
      .exec();

    return rules;
  }

  async removeUniqueName(): Promise<ResponseInterface> {
    let message = '';

    try {
      await this.ruleModel.collection.dropIndex('name_1');
      message += 'name_1 from RULES dropped successfully | ';
    } catch (err) {
      message += `RULES: ${err?.message} | `;
    }

    try {
      await this.eventModel.collection.dropIndex('name_1');
      message += 'name_1 from EVENTS dropped successfully | ';
    } catch (err) {
      message += `EVENTS: ${err?.message} | `;
    }

    try {
      await this.conditionsModel.collection.dropIndex('name_1');
      message += 'name_1 from CONDITIONS dropped successfully | ';
    } catch (err) {
      message += `CONDITIONS: ${err?.message} | `;
    }

    return createResponse(HttpStatus.OK, message);
  }

  async postRules(
    updateRuleDto: QueryOptions,
    rules: 'all' | string,
    rulesPackage: string | undefined
  ): Promise<RuleDocument[]> {
    const updateAllRules = rules === 'all';
    const rulesArr = rules.split(',');
    const ruleQuery = updateAllRules
      ? { rulesPackage: rulesPackage || { $exists: false } }
      : {
          name: { $in: rulesArr },
          rulesPackage: rulesPackage || { $exists: false }
        };

    await this.validateToBeFoundRules(rulesArr, ruleQuery, updateAllRules);

    const updateResult = await this.ruleModel
      .updateMany(ruleQuery, updateRuleDto)
      .populate(['event', 'conditions'])
      .exec();
    if (!updateResult.acknowledged || updateResult.modifiedCount < rulesArr.length) {
      throw this.createNotUpdatedError(rulesPackage, rulesArr, updateAllRules);
    }

    const filterChangedDocuments = updateAllRules ? {} : { name: { $in: rulesArr } };
    const changedDocuments = await this.ruleModel.find(filterChangedDocuments);

    return changedDocuments;
  }

  private createNotUpdatedError(
    rulesPackage: string | undefined,
    rules: string[],
    updateAllRules: boolean
  ) {
    const optionalMessagePart = rulesPackage ? ` with rulesPackage = ${rulesPackage} ` : ' ';
    const suffix = 'were not updated';
    const ruleList = updateAllRules ? 'All Rules' : `Rules ${rules.join(', ')}`;
    const message = `${ruleList}${optionalMessagePart}${suffix}`;

    return CreateException({
      message,
      statusCode: HttpStatus.NOT_FOUND
    });
  }

  private async validateToBeFoundRules(
    rulesArr: string[],
    ruleQuery: object,
    updateAllRules: boolean
  ): Promise<void> {
    if (!updateAllRules) {
      const foundDocuments = await this.ruleModel.find(ruleQuery);

      if (foundDocuments.length !== rulesArr.length) {
        const foundRuleNames = foundDocuments.map(({ name }) => name);
        const notFoundRules = rulesArr.filter(name => !foundRuleNames.includes(name));

        throw CreateException({
          message: `Cannot update not existing rules: ${notFoundRules.join(', ')}`,
          statusCode: HttpStatus.BAD_REQUEST
        });
      }
    }
  }

  async dropAllIndexes(): Promise<ResponseInterface> {
    let message = '';

    try {
      await this.ruleModel.collection.dropIndexes();
      message += 'All indexes from RULES dropped successfully | ';
    } catch (err) {
      message += `RULES: ${err?.message} | `;
    }

    try {
      await this.eventModel.collection.dropIndexes();
      message += 'All indexes from EVENTS dropped successfully | ';
    } catch (err) {
      message += `EVENTS: ${err?.message} | `;
    }

    try {
      await this.conditionsModel.collection.dropIndexes();
      message += 'All indexes from CONDITIONS dropped successfully | ';
    } catch (err) {
      message += `CONDITIONS: ${err?.message} | `;
    }

    return createResponse(HttpStatus.OK, message);
  }

  async getAllIndexes() {
    try {
      const ruleIndexes = await this.ruleModel.collection.indexes();
      const eventIndexes = await this.eventModel.collection.indexes();
      const conditionIndexes = await this.conditionsModel.collection.indexes();

      return [{ ruleIndexes }, { eventIndexes }, { conditionIndexes }];
    } catch (err) {
      return createResponse(HttpStatus.BAD_REQUEST, `Get Indexes were wrong! ${err?.message}`);
    }
  }

  async createAllIndexes() {
    try {
      const ruleIndexes = await this.ruleModel.collection.createIndex(
        { name: 1, rulesPackage: 1 },
        { unique: true }
      );

      const eventIndexes = await this.eventModel.collection.createIndex(
        { name: 1, rulesPackage: 1 },
        { unique: true }
      );

      const conditionIndexes = await this.conditionsModel.collection.createIndex(
        { name: 1, rulesPackage: 1 },
        { unique: true }
      );

      return createResponse(
        HttpStatus.OK,
        'Unique indexes for collections [rule, event, condition] created with success!'
      );
    } catch (err) {
      return createResponse(
        HttpStatus.BAD_REQUEST,
        `Indexes creating were wrong! ${err?.message}`
      );
    }
  }

  async getDuplications() {
    const dataModels = [this.ruleModel, this.eventModel, this.conditionsModel];
    let totalDuplications = 0;

    try {
      const duplications = await Promise.all(
        dataModels.map(async model => {
          const dup = await model.aggregate([
            {
              $group: {
                _id: { name: '$name', rulesPackage: '$rulesPackage' },
                count: { $sum: 1 },
                docs: { $push: '$$ROOT' }
              }
            },
            {
              $match: {
                count: { $gt: 1 }
              }
            }
          ]);

          totalDuplications += dup.length;

          return {
            collection: model.modelName,
            duplications: dup,
            length: dup.length
          };
        })
      );

      if (totalDuplications <= 0) {
        return createResponse(
          HttpStatus.CONTINUE,
          'Collections [rule, event, condition] has no duplication documents within name and rulesPacakge fields.'
        );
      }

      return duplications;
    } catch (err) {
      return createResponse(HttpStatus.BAD_REQUEST, `Something went wrong! ${err?.message}`);
    }
  }

  async getCommonRulesForServices(
    servicesList: string[],
    searchQuery: SearchQueryName
  ): Promise<RuleDocument[] | string[]> {
    const returnOnlyNames = searchQuery.onlyNames;

    const options = getLikeQueryOptions(['name', 'rulesPackage'], searchQuery);

    const commonRules: RuleDocument[] = await this.ruleModel
      .find({
        ...options,
        serviceId: { $all: servicesList }
      })
      .exec();

    if (!commonRules || commonRules.length == 0) {
      throw new NotFoundException(`Common rules for services [${servicesList}] not found!`);
    }

    if (returnOnlyNames) {
      return commonRules.map(rule => `${rule.name} - ${rule.rulesPackage || 'original'}`);
    }

    return commonRules;
  }

  async cloneRule(
    rule: cloneRule,
    rulesPackageToCloneFrom: string | undefined
  ): Promise<RuleDocument> {
    const { name, newName, modifiers } = rule;

    const existingRule = await this.ruleModel
      .findOne({ name, rulesPackage: rulesPackageToCloneFrom })
      .lean();

    if (!existingRule) {
      throw CreateException({
        message: `Rule ${name} not found!`,
        statusCode: HttpStatus.NOT_FOUND
      });
    }

    const { _id, id, rulesPackage, ...ruleToClone } = existingRule;

    let clonedRule: RuleDocument;

    try {
      clonedRule = new this.ruleModel({
        ...ruleToClone,
        ...modifiers,
        name: newName
      });

      await clonedRule.save();
    } catch (err) {
      throw CreateException({
        message: err?.message,
        statusCode: HttpStatus.BAD_REQUEST
      });
    }

    if (rulesPackageToCloneFrom) {
      try {
        const ruleNamesFromPackage = (
          await this.rulesPackagesService.getRulesFromPackageByName(rulesPackageToCloneFrom)
        )?.map(rule => rule.name);

        await this.rulesPackagesService.updateRulesPackage(rulesPackageToCloneFrom, {
          name: rulesPackageToCloneFrom,
          rules: [...ruleNamesFromPackage, newName]
        });
      } catch (error) {
        if (error instanceof HttpException) {
          throw error;
        }

        throw CreateException({
          message: error?.message,
          statusCode: HttpStatus.BAD_REQUEST
        });
      } finally {
        // only one rule stays. Creation of new rule is done by updateRulesPackage
        await clonedRule.deleteOne();
      }
    }

    return clonedRule;
  }
}
