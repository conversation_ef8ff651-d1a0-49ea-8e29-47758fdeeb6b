import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import {
  Conditions,
  ConditionsSchema,
  Event,
  Rule,
  RuleSchema,
  ServiceToPackageMap
} from 'ads-layouts-tools';
import { RulesPackagesModule } from '../rulesPackages/rulesPackages.module';
import { EventSchema, ServiceToPackageMapSchema } from '../schema/dbIndices';
import { RuleController } from './rules.controller';
import { RuleService } from './rules.service';
import { ConditionModule } from '../conditions/conditions.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: Rule.name,
        schema: RuleSchema
      },
      {
        name: Event.name,
        schema: EventSchema
      },
      {
        name: Conditions.name,
        schema: ConditionsSchema
      },
      {
        name: ServiceToPackageMap.name,
        schema: ServiceToPackageMapSchema
      }
    ]),
    ConditionModule,
    RulesPackagesModule
  ],
  controllers: [RuleController],
  providers: [RuleService],
  exports: [RuleService]
})
export class RulesModule {}
